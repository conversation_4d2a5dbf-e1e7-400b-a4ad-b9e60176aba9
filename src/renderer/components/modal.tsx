import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useRef } from 'react';

type ModalProps = {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showCloseButton?: boolean;
};

export function Modal({ isOpen, onClose, children, size = 'md', showCloseButton = true }: ModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';

      // Focus trap - focus the modal when it opens
      if (modalRef.current) {
        modalRef.current.focus();
      }
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  // Get responsive classes based on size
  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return 'max-w-sm w-full sm:w-auto';
      case 'md':
        return 'max-w-md w-full sm:w-auto md:max-w-lg';
      case 'lg':
        return 'max-w-lg w-full sm:w-auto md:max-w-xl lg:max-w-2xl';
      case 'xl':
        return 'max-w-xl w-full sm:w-auto md:max-w-2xl lg:max-w-4xl xl:max-w-5xl';
      case 'full':
        return 'w-full h-full max-w-none max-h-none';
      default:
        return 'max-w-md w-full sm:w-auto md:max-w-lg';
    }
  };

  const getHeightClasses = (size: string) => {
    switch (size) {
      case 'full':
        return 'h-full max-h-none';
      default:
        return 'max-h-[85vh] sm:max-h-[90vh]';
    }
  };

  const getPaddingClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return 'p-4 sm:p-6';
      case 'full':
        return 'p-4 sm:p-6 md:p-8';
      default:
        return 'p-4 sm:p-6 md:p-8';
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 p-2 sm:p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
          style={{ zIndex: 10000 }}
        >
          <motion.div
            ref={modalRef}
            className={`
              bg-white rounded-lg sm:rounded-xl shadow-xl overflow-y-auto max-w-lg w-full
              ${getHeightClasses(size)}
              ${getPaddingClasses(size)}
              relative
              focus:outline-none
            `}
            initial={{ 
              y: size === 'full' ? '100%' : 50, 
              opacity: 0,
              scale: size === 'full' ? 1 : 0.95
            }}
            animate={{ 
              y: 0, 
              opacity: 1,
              scale: 1
            }}
            exit={{ 
              y: size === 'full' ? '100%' : 50, 
              opacity: 0,
              scale: size === 'full' ? 1 : 0.95
            }}
            transition={{
              type: "spring",
              damping: 25,
              stiffness: 300
            }}
            onClick={(e) => e.stopPropagation()}
            tabIndex={-1}
          >
            {/* Close Button */}
            {showCloseButton && (
              <button
                onClick={onClose}
                className="absolute top-3 right-3 sm:top-4 sm:right-4 p-1 rounded-full hover:bg-gray-100 transition-colors z-10 focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label="Close modal"
              >
                <svg 
                  className="w-5 h-5 sm:w-6 sm:h-6 text-gray-400 hover:text-gray-600" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M6 18L18 6M6 6l12 12" 
                  />
                </svg>
              </button>
            )}

            {/* Modal Content */}
            <div>
              {children}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}