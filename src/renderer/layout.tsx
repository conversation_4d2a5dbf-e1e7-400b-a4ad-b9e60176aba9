import { NavLink, Outlet } from 'react-router-dom'
import { useState } from 'react'

import { Separator } from './components/ui/separator'
import { useAuth, useIsAdmin } from './contexts/AuthContext'
import { Button } from './components/button'
import { NotificationContainer } from './components/NotificationContainer'

export function Layout() {
  const { user, logout } = useAuth();
  const isAdmin = useIsAdmin();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <main className="flex flex-col items-center gap-5 mt-1">
      <nav className="flex w-full gap-2 p-4 sticky top-0 bg-white z-50 shadow justify-between">
        <div className="flex gap-2 items-center">
          <NavLink to="/">Main</NavLink>

          <Separator orientation="vertical" />

          <NavLink to="/about">About</NavLink>

          <Separator orientation="vertical" />

          <NavLink to="/example-usage">PDF Example</NavLink>

          <Separator orientation="vertical" />

          <NavLink to="/csv-upload">CSV Upload</NavLink>

          <Separator orientation="vertical" />

          <NavLink to="/csv-folder-monitor">CSV Monitor</NavLink>

          <Separator orientation="vertical" />

          <NavLink to="/postgresql-test">PostgreSQL Test</NavLink>

          <Separator orientation="vertical" />

          <NavLink to="/pcloud-test">pCloud Test</NavLink>

          <Separator orientation="vertical" />

          <NavLink to="/bank-master">Bank Master</NavLink>

          {isAdmin && (
            <>
              <Separator orientation="vertical" />
              <NavLink to="/user-management">User Management</NavLink>
            </>
          )}
        </div>

        {/* User Info and Logout */}
        <div className="flex items-center gap-4">
          <div className="text-sm text-gray-600">
            <span className="font-medium">{user?.user_name}</span>
            {user?.role_name && (
              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                {user.role_name}
              </span>
            )}
          </div>

          <Button
            variant="secondary"
            size="sm"
            onClick={handleLogout}
            disabled={isLoggingOut}
            className="text-sm"
          >
            {isLoggingOut ? 'Logging out...' : 'Logout'}
          </Button>
        </div>
      </nav>

      <section className="items-center justify-center flex flex-col gap-6">
        <Outlet />
      </section>

      {/* Global Notification Container */}
      <NotificationContainer />
    </main>
  )
}
