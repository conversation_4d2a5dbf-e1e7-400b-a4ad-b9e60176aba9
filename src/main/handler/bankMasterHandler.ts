import { ipcMain } from 'electron';
import { Client } from 'pg';
import { getDbConnection, PostgreSQLConnectionConfig } from '../db';
import { 
  getCurrentThailandTimestamp, 
  selectThailandTimestamp, 
  getThailandDateString, 
  getThailandTimeString 
} from '../utils/timezone';

interface BankMaster {
  ftbnkid?: number;
  ftbnkcode: string;
  ftbnkname: string;
  ftstaactive: string;
  fddateupd?: string;
  fttimeupd?: string;
  ftwhoupd?: string;
  fddateins?: string;
  fttimeins?: string;
  ftwhoins?: string;
}

interface BankMasterResponse {
  success: boolean;
  message: string;
  data?: BankMaster | BankMaster[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}

interface PaginationParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export function setupBankMasterHandlers() {
  console.log('Setting up Bank Master handlers...');

  // Get banks with pagination
  ipcMain.handle('get-banks', async (_event, params: PaginationParams = {}): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      const {
        page = 1,
        pageSize = 10,
        search = '',
        sortBy = 'ftbnkname',
        sortOrder = 'ASC'
      } = params;

      console.log(`📋 Fetching banks - Page: ${page}, Size: ${pageSize}, Search: "${search}"`);
      client = await getDbConnection();

      let whereClause = '';
      let searchParams: any[] = [];
      let paramIndex = 1;

      if (search.trim()) {
        whereClause = `WHERE (
          UPPER(ftbnkcode) LIKE UPPER($${paramIndex}) OR
          UPPER(ftbnkname) LIKE UPPER($${paramIndex + 1})
        )`;
        searchParams = [`%${search}%`, `%${search}%`];
        paramIndex += 2;
      }

      const countQuery = `SELECT COUNT(*) as total FROM tmst_bank ${whereClause}`;
      const countResult = await client.query(countQuery, searchParams);
      const totalRecords = parseInt(countResult.rows[0].total);

      const totalPages = Math.ceil(totalRecords / pageSize);
      const offset = (page - 1) * pageSize;
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      const allowedSortColumns = ['ftbnkid', 'ftbnkcode', 'ftbnkname', 'ftstaactive', 'fddateins', 'fddateupd'];
      const validSortBy = allowedSortColumns.includes(sortBy) ? sortBy : 'ftbnkname';
      const validSortOrder = sortOrder === 'DESC' ? 'DESC' : 'ASC';

      const dataQuery = `
        SELECT ftbnkid, ftbnkcode, ftbnkname, ftstaactive,
               fddateupd, fttimeupd, ftwhoupd,
               fddateins, fttimeins, ftwhoins
        FROM tmst_bank
        ${whereClause}
        ORDER BY ${validSortBy} ${validSortOrder}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const dataParams = [...searchParams, pageSize, offset];
      const result = await client.query(dataQuery, dataParams);

      console.log(`✅ Found ${result.rows.length} banks (Page ${page}/${totalPages}, Total: ${totalRecords})`);

      return {
        success: true,
        message: `Found ${result.rows.length} banks on page ${page}`,
        data: result.rows,
        pagination: {
          currentPage: page,
          totalPages,
          totalRecords,
          pageSize,
          hasNextPage,
          hasPreviousPage
        }
      };

    } catch (error: any) {
      console.error('❌ Error fetching banks:', error.message);
      return {
        success: false,
        message: 'Failed to fetch banks',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Create bank
  ipcMain.handle('create-bank', async (_event, bankData: Omit<BankMaster, 'ftbnkid' | 'fddateins' | 'fttimeins'>): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      console.log('➕ Creating new bank:', bankData.ftbnkname);
      client = await getDbConnection();

      const query = `
        INSERT INTO tmst_bank (ftbnkcode, ftbnkname, ftstaactive, fddateins, fttimeins, ftwhoins)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING ftbnkid, ftbnkcode, ftbnkname, ftstaactive,
                  fddateupd, fttimeupd, ftwhoupd,
                  fddateins, fttimeins, ftwhoins
      `;

      const values = [
        bankData.ftbnkcode.toUpperCase(),
        bankData.ftbnkname,
        bankData.ftstaactive,
        getThailandDateString(),
        getThailandTimeString(),
        bankData.ftwhoins || 'SYSTEM'
      ];

      const result = await client.query(query, values);
      console.log('✅ Bank created successfully:', result.rows[0].ftbnkname);

      return {
        success: true,
        message: 'Bank created successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error creating bank:', error.message);

      let errorMessage = 'Failed to create bank';
      if (error.code === '23505') {
        errorMessage = 'Bank code already exists';
      } else if (error.message.includes('value too long')) {
        errorMessage = 'Bank code must be 5 characters or less';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Update bank
  ipcMain.handle('update-bank', async (_event, bankId: number, bankData: Omit<BankMaster, 'ftbnkid' | 'fddateins' | 'fttimeins'>): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      console.log('✏️ Updating bank ID:', bankId);
      client = await getDbConnection();

      const query = `
        UPDATE tmst_bank
        SET ftbnkcode = $1, ftbnkname = $2, ftstaactive = $3,
            fddateupd = $4, fttimeupd = $5, ftwhoupd = $6
        WHERE ftbnkid = $7
        RETURNING ftbnkid, ftbnkcode, ftbnkname, ftstaactive,
                  fddateupd, fttimeupd, ftwhoupd,
                  fddateins, fttimeins, ftwhoins
      `;

      const values = [
        bankData.ftbnkcode.toUpperCase(),
        bankData.ftbnkname,
        bankData.ftstaactive || '1',
        getThailandDateString(),
        getThailandTimeString(),
        bankData.ftwhoupd || 'SYSTEM',
        bankId
      ];

      const result = await client.query(query, values);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'Bank not found',
          error: 'BANK_NOT_FOUND'
        };
      }

      console.log('✅ Bank updated successfully:', result.rows[0].ftbnkname);

      return {
        success: true,
        message: 'Bank updated successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error updating bank:', error.message);

      let errorMessage = 'Failed to update bank';
      if (error.code === '23505') {
        errorMessage = 'Bank code already exists';
      } else if (error.message.includes('value too long')) {
        errorMessage = 'Bank code must be 5 characters or less';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Delete bank
  ipcMain.handle('delete-bank', async (_event, bankId: number): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      console.log('🗑️ Deleting bank ID:', bankId);
      client = await getDbConnection();

      const query = `
        DELETE FROM tmst_bank
        WHERE ftbnkid = $1
        RETURNING ftbnkname
      `;

      const result = await client.query(query, [bankId]);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'Bank not found',
          error: 'BANK_NOT_FOUND'
        };
      }

      console.log('✅ Bank deleted successfully:', result.rows[0].ftbnkname);

      return {
        success: true,
        message: 'Bank deleted successfully'
      };

    } catch (error: any) {
      console.error('❌ Error deleting bank:', error.message);
      return {
        success: false,
        message: 'Failed to delete bank',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });
}
